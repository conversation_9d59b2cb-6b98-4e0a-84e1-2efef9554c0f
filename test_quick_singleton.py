"""
Quick test để verify singleton pattern cho một vài services chính
"""

def test_openrouter():
    print("🧪 Testing OpenRouter Service...")
    try:
        from app.services.openrouter_service import OpenRouterService, openrouter_service
        
        # Test basic singleton
        s1 = OpenRouterService()
        s2 = OpenRouterService()
        
        print(f"   Instance 1: {id(s1)}")
        print(f"   Instance 2: {id(s2)}")
        print(f"   Global:     {id(openrouter_service)}")
        
        if s1 is s2 is openrouter_service:
            print("   ✅ OpenRouter singleton works")
            return True
        else:
            print("   ❌ OpenRouter singleton failed")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_llm():
    print("\n🧪 Testing LLM Service...")
    try:
        from app.services.llm_service import LLMService, llm_service
        
        # Test basic singleton
        s1 = LLMService()
        s2 = LLMService()
        
        print(f"   Instance 1: {id(s1)}")
        print(f"   Instance 2: {id(s2)}")
        print(f"   Global:     {id(llm_service)}")
        
        if s1 is s2 is llm_service:
            print("   ✅ LLM singleton works")
            return True
        else:
            print("   ❌ LLM singleton failed")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_cv_parser():
    print("\n🧪 Testing CV Parser Service...")
    try:
        from app.services.cv_parser_service import CVParserService, cv_parser_service
        
        # Test basic singleton
        s1 = CVParserService()
        s2 = CVParserService()
        
        print(f"   Instance 1: {id(s1)}")
        print(f"   Instance 2: {id(s2)}")
        print(f"   Global:     {id(cv_parser_service)}")
        
        if s1 is s2 is cv_parser_service:
            print("   ✅ CV Parser singleton works")
            return True
        else:
            print("   ❌ CV Parser singleton failed")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def main():
    print("🚀 Quick Singleton Test")
    print("=" * 40)
    
    results = []
    results.append(("OpenRouter", test_openrouter()))
    results.append(("LLM", test_llm()))
    results.append(("CVParser", test_cv_parser()))
    
    print("\n" + "=" * 40)
    print("📊 Results:")
    
    passed = 0
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"   {name:<12} {status}")
        if result:
            passed += 1
    
    print("=" * 40)
    print(f"Passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tested services use singleton!")
    else:
        print("⚠️ Some services need fixing!")


if __name__ == "__main__":
    main()
