"""
Simple test để check services có sử dụng singleton pattern không
"""

def test_openrouter_service():
    """Test OpenRouter service singleton"""
    print("🧪 Testing OpenRouter Service...")
    try:
        from app.services.openrouter_service import OpenRouterService, openrouter_service
        
        instance1 = OpenRouterService()
        instance2 = OpenRouterService()
        
        print(f"   Instance1 ID: {id(instance1)}")
        print(f"   Instance2 ID: {id(instance2)}")
        print(f"   Global ID: {id(openrouter_service)}")
        
        if instance1 is instance2 and instance1 is openrouter_service:
            print("   ✅ OpenRouter Service singleton works")
            return True
        else:
            print("   ❌ OpenRouter Service singleton failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_llm_service():
    """Test LLM service singleton"""
    print("\n🧪 Testing LLM Service...")
    try:
        from app.services.llm_service import LLMService, llm_service
        
        instance1 = LLMService()
        instance2 = LLMService()
        
        print(f"   Instance1 ID: {id(instance1)}")
        print(f"   Instance2 ID: {id(instance2)}")
        print(f"   Global ID: {id(llm_service)}")
        
        if instance1 is instance2 and instance1 is llm_service:
            print("   ✅ LLM Service singleton works")
            return True
        else:
            print("   ❌ LLM Service singleton failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_kafka_service():
    """Test Kafka service singleton"""
    print("\n🧪 Testing Kafka Service...")
    try:
        from app.services.kafka_service import KafkaService, kafka_service
        
        instance1 = KafkaService()
        instance2 = KafkaService()
        
        print(f"   Instance1 ID: {id(instance1)}")
        print(f"   Instance2 ID: {id(instance2)}")
        print(f"   Global ID: {id(kafka_service)}")
        
        if instance1 is instance2 and instance1 is kafka_service:
            print("   ✅ Kafka Service singleton works")
            return True
        else:
            print("   ❌ Kafka Service singleton failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_smart_exam_service():
    """Test Smart Exam service singleton"""
    print("\n🧪 Testing Smart Exam Service...")
    try:
        from app.services.smart_exam_generation_service import SmartExamGenerationService, smart_exam_generation_service
        
        instance1 = SmartExamGenerationService()
        instance2 = SmartExamGenerationService()
        
        print(f"   Instance1 ID: {id(instance1)}")
        print(f"   Instance2 ID: {id(instance2)}")
        print(f"   Global ID: {id(smart_exam_generation_service)}")
        
        if instance1 is instance2 and instance1 is smart_exam_generation_service:
            print("   ✅ Smart Exam Service singleton works")
            return True
        else:
            print("   ❌ Smart Exam Service singleton failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_exam_service():
    """Test Exam service singleton"""
    print("\n🧪 Testing Exam Service...")
    try:
        from app.services.exam_generation_service import ExamGenerationService, exam_generation_service
        
        instance1 = ExamGenerationService()
        instance2 = ExamGenerationService()
        
        print(f"   Instance1 ID: {id(instance1)}")
        print(f"   Instance2 ID: {id(instance2)}")
        print(f"   Global ID: {id(exam_generation_service)}")
        
        if instance1 is instance2 and instance1 is exam_generation_service:
            print("   ✅ Exam Service singleton works")
            return True
        else:
            print("   ❌ Exam Service singleton failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Testing Services Singleton Pattern")
    print("=" * 50)
    
    results = []
    
    # Test each service
    results.append(("OpenRouter", test_openrouter_service()))
    results.append(("LLM", test_llm_service()))
    results.append(("Kafka", test_kafka_service()))
    results.append(("SmartExam", test_smart_exam_service()))
    results.append(("Exam", test_exam_service()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Results Summary:")
    print("=" * 50)
    
    passed = 0
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {name:<15} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"✅ Passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All services use singleton pattern!")
    else:
        print("⚠️ Some services need singleton implementation!")


if __name__ == "__main__":
    main()
