"""
Test tất cả services để verify singleton pattern
"""

import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor


def test_service_singleton(service_class, service_name):
    """Test singleton pattern cho 1 service"""
    print(f"\n🧪 Testing {service_name} singleton pattern...")
    
    try:
        # Test 1: Basic singleton
        instance1 = service_class()
        instance2 = service_class()
        
        assert instance1 is instance2, f"❌ {service_name} instances should be the same"
        print(f"   ✅ {service_name} basic singleton works")
        
        # Test 2: Multi-threading
        instances = []
        
        def create_instance():
            instances.append(service_class())
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_instance) for _ in range(5)]
            for future in futures:
                future.result()
        
        # All instances should be the same
        first_instance = instances[0]
        for i, instance in enumerate(instances[1:], 2):
            assert instance is first_instance, f"❌ {service_name} thread instance {i} is different"
        
        print(f"   ✅ {service_name} thread-safe singleton works")
        
        return True
        
    except Exception as e:
        print(f"   ❌ {service_name} singleton test failed: {e}")
        return False


def test_global_instance_singleton(service_module, service_class, global_instance_name):
    """Test global instance singleton"""
    service_name = service_class.__name__
    print(f"\n🧪 Testing {service_name} global instance...")
    
    try:
        # Get global instance
        global_instance = getattr(service_module, global_instance_name)
        
        # Create new instance
        new_instance = service_class()
        
        print(f"   Global instance ID: {id(global_instance)}")
        print(f"   New instance ID: {id(new_instance)}")
        
        assert global_instance is new_instance, f"❌ {service_name} global instance should be same as new instance"
        print(f"   ✅ {service_name} global instance works correctly")
        
        return True
        
    except Exception as e:
        print(f"   ❌ {service_name} global instance test failed: {e}")
        return False


def main():
    """Test tất cả services"""
    print("🚀 Testing All Services Singleton Pattern")
    print("=" * 60)
    
    test_results = []
    
    # Test OpenRouter Service
    try:
        from app.services.openrouter_service import OpenRouterService
        import app.services.openrouter_service as openrouter_module
        
        result1 = test_service_singleton(OpenRouterService, "OpenRouterService")
        result2 = test_global_instance_singleton(openrouter_module, OpenRouterService, "openrouter_service")
        test_results.append(("OpenRouterService", result1 and result2))
        
    except Exception as e:
        print(f"❌ OpenRouterService test failed: {e}")
        test_results.append(("OpenRouterService", False))
    
    # Test LLM Service
    try:
        from app.services.llm_service import LLMService
        import app.services.llm_service as llm_module
        
        result1 = test_service_singleton(LLMService, "LLMService")
        result2 = test_global_instance_singleton(llm_module, LLMService, "llm_service")
        test_results.append(("LLMService", result1 and result2))
        
    except Exception as e:
        print(f"❌ LLMService test failed: {e}")
        test_results.append(("LLMService", False))
    
    # Test Kafka Service
    try:
        from app.services.kafka_service import KafkaService
        import app.services.kafka_service as kafka_module
        
        result1 = test_service_singleton(KafkaService, "KafkaService")
        result2 = test_global_instance_singleton(kafka_module, KafkaService, "kafka_service")
        test_results.append(("KafkaService", result1 and result2))
        
    except Exception as e:
        print(f"❌ KafkaService test failed: {e}")
        test_results.append(("KafkaService", False))
    
    # Test Smart Exam Generation Service
    try:
        from app.services.smart_exam_generation_service import SmartExamGenerationService
        import app.services.smart_exam_generation_service as smart_exam_module
        
        result1 = test_service_singleton(SmartExamGenerationService, "SmartExamGenerationService")
        result2 = test_global_instance_singleton(smart_exam_module, SmartExamGenerationService, "smart_exam_generation_service")
        test_results.append(("SmartExamGenerationService", result1 and result2))
        
    except Exception as e:
        print(f"❌ SmartExamGenerationService test failed: {e}")
        test_results.append(("SmartExamGenerationService", False))
    
    # Test Exam Generation Service
    try:
        from app.services.exam_generation_service import ExamGenerationService
        import app.services.exam_generation_service as exam_module
        
        result1 = test_service_singleton(ExamGenerationService, "ExamGenerationService")
        result2 = test_global_instance_singleton(exam_module, ExamGenerationService, "exam_generation_service")
        test_results.append(("ExamGenerationService", result1 and result2))
        
    except Exception as e:
        print(f"❌ ExamGenerationService test failed: {e}")
        test_results.append(("ExamGenerationService", False))
    
    # Test CV Parser Service
    try:
        from app.services.cv_parser_service import CVParserService
        import app.services.cv_parser_service as cv_module
        
        result1 = test_service_singleton(CVParserService, "CVParserService")
        result2 = test_global_instance_singleton(cv_module, CVParserService, "cv_parser_service")
        test_results.append(("CVParserService", result1 and result2))
        
    except Exception as e:
        print(f"❌ CVParserService test failed: {e}")
        test_results.append(("CVParserService", False))
    
    # Print results
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for service_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {service_name:<30} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print("=" * 60)
    print(f"📈 Total: {len(test_results)} services")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All services implement singleton pattern correctly!")
        return True
    else:
        print(f"\n⚠️ {failed} services need singleton pattern implementation!")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
