#!/usr/bin/env python3
"""
Test script để verify Singleton pattern của OpenRouter service
Chạy script này để kiểm tra xem service chỉ được khởi tạo 1 lần duy nhất
"""

import sys
import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_singleton_basic():
    """Test basic singleton behavior"""
    print("🧪 Testing basic singleton behavior...")
    
    from app.services.openrouter_service import OpenRouterService, get_openrouter_service
    
    # Test 1: Multiple instances should be the same object
    service1 = OpenRouterService()
    service2 = OpenRouterService()
    service3 = get_openrouter_service()
    
    print(f"   Service1 ID: {id(service1)}")
    print(f"   Service2 ID: {id(service2)}")
    print(f"   Service3 ID: {id(service3)}")
    
    assert service1 is service2, "❌ Service1 and Service2 should be the same instance"
    assert service2 is service3, "❌ Service2 and Service3 should be the same instance"
    
    print("   ✅ All instances are the same object")
    return service1

def test_singleton_threading():
    """Test singleton behavior in multi-threading environment"""
    print("\n🧪 Testing singleton in multi-threading...")
    
    instances = []
    
    def create_instance():
        from app.services.openrouter_service import OpenRouterService
        instance = OpenRouterService()
        instances.append(instance)
        time.sleep(0.1)  # Simulate some work
        return instance
    
    # Create multiple threads
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(create_instance) for _ in range(10)]
        results = [future.result() for future in futures]
    
    # Check all instances are the same
    first_instance = results[0]
    for i, instance in enumerate(results):
        print(f"   Thread {i+1} instance ID: {id(instance)}")
        assert instance is first_instance, f"❌ Instance {i+1} is different from first instance"
    
    print("   ✅ All thread instances are the same object")
    return first_instance

def test_initialization_count():
    """Test that initialization only happens once"""
    print("\n🧪 Testing initialization count...")
    
    # Reset instance for clean test
    from app.services.openrouter_service import OpenRouterService
    OpenRouterService.reset_instance()
    
    print("   Creating multiple instances...")
    instances = []
    for i in range(5):
        instance = OpenRouterService()
        instances.append(instance)
        print(f"   Instance {i+1} created")
    
    # All should be the same
    first_instance = instances[0]
    for i, instance in enumerate(instances[1:], 2):
        assert instance is first_instance, f"❌ Instance {i} is different"
    
    print("   ✅ Only one initialization occurred")

def test_global_instance():
    """Test global instance behavior"""
    print("\n🧪 Testing global instance...")

    # Import fresh to get current global instance
    import importlib
    import app.services.openrouter_service
    importlib.reload(app.services.openrouter_service)

    from app.services.openrouter_service import openrouter_service, OpenRouterService

    # Global instance should be same as new instance (due to singleton)
    new_instance = OpenRouterService()

    print(f"   Global instance ID: {id(openrouter_service)}")
    print(f"   New instance ID: {id(new_instance)}")

    assert openrouter_service is new_instance, "❌ Global instance should be same as new instance"
    print("   ✅ Global instance works correctly")

def main():
    """Run all tests"""
    print("🚀 Testing OpenRouter Service Singleton Pattern")
    print("=" * 60)
    
    try:
        # Run tests
        service = test_singleton_basic()
        test_singleton_threading()
        test_initialization_count()
        test_global_instance()
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed!")
        print(f"✅ OpenRouter service singleton pattern works correctly")
        print(f"📊 Service info:")
        print(f"   - Available: {service.is_available()}")
        print(f"   - Model: {service.model}")
        print(f"   - Base URL: {service.base_url}")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
